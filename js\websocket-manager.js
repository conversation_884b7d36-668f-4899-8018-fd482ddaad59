/**
 * WebSocket Manager for Valorant Strategy Board
 * Handles server-based connections and real-time synchronization
 */

class WebSocketManager {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.isHost = false;
        this.sessionCode = null;
        this.sessionName = '';
        this.localUserId = this.generateUserId();
        this.localUserName = 'User_' + this.localUserId.substring(0, 6);
        this.serverUrl = this.getServerUrl();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.maxReconnectDelay = 30000; // Max 30 seconds
        this.heartbeatInterval = null;
        this.connectionTimeout = null;
        
        // Event callbacks
        this.onConnectionEstablished = null;
        this.onConnectionLost = null;
        this.onDataReceived = null;
        this.onUserJoined = null;
        this.onUserLeft = null;
        this.onError = null;
        
        console.log('WebSocket Manager initialized with user ID:', this.localUserId);
        console.log('Server URL:', this.serverUrl);
    }
    
    /**
     * Generate a unique user ID
     */
    generateUserId() {
        return 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    }
    
    /**
     * Get the WebSocket server URL
     */
    getServerUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        // Use port 8080 for local development, or same port as web server in production
        const port = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' ? ':8080' : '';
        return `${protocol}//${host}${port}`;
    }
    
    /**
     * Connect to the WebSocket server
     */
    connect() {
        return new Promise((resolve, reject) => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                resolve();
                return;
            }

            console.log('Connecting to WebSocket server:', this.serverUrl);
            
            // Clear any existing connection timeout
            if (this.connectionTimeout) {
                clearTimeout(this.connectionTimeout);
            }

            // Set connection timeout
            this.connectionTimeout = setTimeout(() => {
                if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
                    this.ws.close();
                    reject(new Error('Connection timeout'));
                }
            }, 10000); // 10 second timeout

            try {
                this.ws = new WebSocket(this.serverUrl);
                
                this.ws.onopen = () => {
                    console.log('WebSocket connected');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.reconnectDelay = 1000;
                    
                    if (this.connectionTimeout) {
                        clearTimeout(this.connectionTimeout);
                        this.connectionTimeout = null;
                    }
                    
                    this.startHeartbeat();
                    resolve();
                };
                
                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleMessage(data);
                    } catch (error) {
                        console.error('Error parsing WebSocket message:', error);
                    }
                };
                
                this.ws.onclose = (event) => {
                    console.log('WebSocket disconnected:', event.code, event.reason);
                    this.handleDisconnection();
                    
                    if (this.connectionTimeout) {
                        clearTimeout(this.connectionTimeout);
                        this.connectionTimeout = null;
                    }
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    
                    if (this.connectionTimeout) {
                        clearTimeout(this.connectionTimeout);
                        this.connectionTimeout = null;
                    }
                    
                    if (this.onError) {
                        this.onError(error);
                    }
                    reject(error);
                };
                
            } catch (error) {
                console.error('Error creating WebSocket:', error);
                if (this.onError) {
                    this.onError(error);
                }
                reject(error);
            }
        });
    }
    
    /**
     * Handle incoming messages from server
     */
    handleMessage(data) {
        switch (data.type) {
            case 'connection-established':
                console.log('Connection established with server');
                break;
                
            case 'session-created':
                this.sessionCode = data.sessionCode;
                this.sessionName = data.sessionName;
                this.isHost = data.isHost;
                console.log('Session created:', this.sessionCode);
                if (this.onConnectionEstablished) {
                    this.onConnectionEstablished();
                }
                break;
                
            case 'session-joined':
                this.sessionCode = data.sessionCode;
                this.sessionName = data.sessionName;
                this.isHost = data.isHost;
                console.log('Joined session:', this.sessionCode);
                if (this.onConnectionEstablished) {
                    this.onConnectionEstablished();
                }
                break;
                
            case 'session-creation-failed':
            case 'session-join-failed':
                console.error('Session operation failed:', data.message);
                if (this.onError) {
                    this.onError(new Error(data.message));
                }
                break;
                
            case 'user-join':
                console.log('User joined:', data.userName);
                if (this.onUserJoined) {
                    this.onUserJoined(data);
                }
                // Also trigger onDataReceived for backward compatibility
                if (this.onDataReceived) {
                    this.onDataReceived(data, data.fromClientId);
                }
                break;

            case 'user-leave':
                console.log('User left:', data.userName);
                if (this.onUserLeft) {
                    this.onUserLeft(data);
                }
                // Also trigger onDataReceived for backward compatibility
                if (this.onDataReceived) {
                    this.onDataReceived(data, data.fromClientId);
                }
                break;
                
            case 'error':
                console.error('Server error:', data.message);
                if (this.onError) {
                    this.onError(new Error(data.message));
                }
                break;
                
            default:
                // Handle collaboration messages
                if (this.onDataReceived) {
                    this.onDataReceived(data, data.fromClientId);
                }
                break;
        }
    }
    
    /**
     * Handle WebSocket disconnection
     */
    handleDisconnection() {
        console.log('WebSocket disconnection detected');
        this.isConnected = false;
        this.stopHeartbeat();

        if (this.onConnectionLost) {
            console.log('Calling onConnectionLost callback');
            this.onConnectionLost();
        }

        // Attempt to reconnect if we were in a session
        if (this.sessionCode && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
        }
    }
    
    /**
     * Attempt to reconnect to the server
     */
    attemptReconnect() {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect()
                .then(() => {
                    console.log('Reconnected successfully');
                    // Rejoin session if we had one
                    if (this.sessionCode) {
                        if (this.isHost) {
                            return this.createSession(this.sessionName);
                        } else {
                            return this.joinSession(this.sessionCode);
                        }
                    }
                })
                .catch((error) => {
                    console.error('Reconnection failed:', error);
                    if (this.reconnectAttempts < this.maxReconnectAttempts) {
                        // Exponential backoff
                        this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay);
                        this.attemptReconnect();
                    } else {
                        console.error('Max reconnection attempts reached');
                        if (this.onError) {
                            this.onError(new Error('Failed to reconnect to server'));
                        }
                    }
                });
        }, this.reconnectDelay);
    }
    
    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000); // Send ping every 30 seconds
    }
    
    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    /**
     * Send a message to the server
     */
    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            try {
                this.ws.send(JSON.stringify(data));
                return true;
            } catch (error) {
                console.error('Error sending message:', error);
                return false;
            }
        } else {
            console.warn('WebSocket not connected, cannot send message');
            return false;
        }
    }

    /**
     * Create a new collaboration session
     */
    async createSession(sessionName = 'Strategy Session') {
        try {
            if (!this.isConnected) {
                await this.connect();
            }

            this.sessionName = sessionName;
            console.log('Creating session:', sessionName);

            return new Promise((resolve, reject) => {
                const originalOnConnectionEstablished = this.onConnectionEstablished;
                const originalOnError = this.onError;

                // Set up temporary handlers for session creation
                this.onConnectionEstablished = () => {
                    this.onConnectionEstablished = originalOnConnectionEstablished;
                    this.onError = originalOnError;

                    // Send user join notification for host
                    this.send({
                        type: 'user-join',
                        userId: this.localUserId,
                        userName: this.localUserName
                    });

                    resolve(this.sessionCode);
                };

                this.onError = (error) => {
                    this.onConnectionEstablished = originalOnConnectionEstablished;
                    this.onError = originalOnError;
                    reject(error);
                };

                // Send create session request
                this.send({
                    type: 'create-session',
                    sessionName: sessionName
                });
            });
        } catch (error) {
            console.error('Failed to create session:', error);
            throw error;
        }
    }

    /**
     * Join an existing collaboration session
     */
    async joinSession(sessionCode) {
        try {
            if (!this.isConnected) {
                await this.connect();
            }

            this.sessionCode = sessionCode.toUpperCase();
            console.log('Joining session:', this.sessionCode);

            return new Promise((resolve, reject) => {
                const originalOnConnectionEstablished = this.onConnectionEstablished;
                const originalOnError = this.onError;

                // Set up temporary handlers for session join
                this.onConnectionEstablished = () => {
                    this.onConnectionEstablished = originalOnConnectionEstablished;
                    this.onError = originalOnError;

                    // Send user join notification
                    this.send({
                        type: 'user-join',
                        userId: this.localUserId,
                        userName: this.localUserName
                    });

                    resolve();
                };

                this.onError = (error) => {
                    this.onConnectionEstablished = originalOnConnectionEstablished;
                    this.onError = originalOnError;
                    reject(error);
                };

                // Send join session request
                this.send({
                    type: 'join-session',
                    sessionCode: this.sessionCode
                });
            });
        } catch (error) {
            console.error('Failed to join session:', error);
            throw error;
        }
    }

    /**
     * Leave the current session
     */
    leaveSession() {
        if (this.sessionCode) {
            console.log('Leaving session:', this.sessionCode);

            // Send user leave notification
            this.send({
                type: 'user-leave',
                userId: this.localUserId,
                userName: this.localUserName
            });

            // Send leave session request
            this.send({
                type: 'leave-session'
            });

            this.sessionCode = null;
            this.sessionName = '';
            this.isHost = false;
        }
    }

    /**
     * Broadcast data to all connected peers in the session
     */
    broadcast(data) {
        if (!this.sessionCode) {
            console.warn('Not in a session, cannot broadcast');
            return 0;
        }

        const success = this.send(data);
        return success ? 1 : 0; // Return count for compatibility
    }

    /**
     * Get list of connected peer IDs (compatibility method)
     */
    getConnectedPeers() {
        // In WebSocket implementation, we don't track individual peers
        // This is handled by the server
        return this.isConnected && this.sessionCode ? ['server'] : [];
    }

    /**
     * Check if connected to the server and in a session
     */
    isConnectedToSession() {
        return this.isConnected && this.sessionCode !== null;
    }



    /**
     * Disconnect from the server
     */
    disconnect() {
        console.log('Disconnecting from WebSocket server');

        this.stopHeartbeat();

        if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
        }

        if (this.ws) {
            this.ws.close(1000, 'Client disconnecting');
            this.ws = null;
        }

        this.isConnected = false;
        this.sessionCode = null;
        this.sessionName = '';
        this.isHost = false;
        this.reconnectAttempts = 0;
    }

    /**
     * Get current connection status
     */
    getConnectionStatus() {
        if (!this.ws) return 'disconnected';

        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return this.sessionCode ? 'connected' : 'connected-no-session';
            case WebSocket.CLOSING:
                return 'disconnecting';
            case WebSocket.CLOSED:
                return 'disconnected';
            default:
                return 'unknown';
        }
    }
}
